<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="icon" href="../../Application/Index/View/Public/static/annie/img/fovicon.ico">
    <title>二维码生成器</title>
    <meta name="description" content="二维码生成器">
    <meta name="keywords" content="二维码,QR码,生成器">
    <meta name="author" content="二维码生成器">
    <script defer="defer" src="../../Application/Index/View/Public/static/annie/js/annie.js"></script>
    <link href="../../Application/Index/View/Public/static/annie/css/annie.css" rel="stylesheet">
    <link href="../../Application/Index/View/Public/static/annie/css/app.css" rel="stylesheet">
    <link href="../../Application/Index/View/Public/static/annie/css/annieMod.css" rel="stylesheet">
    <link href="../../Application/Index/View/Public/static/annie/css/utils.css" rel="stylesheet">
    <script src="https://unpkg.com/qrcode-generator@1.4.4/qrcode.js"></script>
    <style>
        .ours-select {
            position: relative
        }

        .ours-select .selection-item {
            display: block;
            cursor: pointer;
            width: 100%;
            height: 36px;
            line-height: 36px;
            box-sizing: border-box;
            padding: 0 32px 0 10px;
            position: relative
        }

        /* PC端优化样式 */
        @media (min-width: 768px) {
            .YF28[data-v-7cd14dad] {
                max-width: 1200px;
                margin: 0 auto;
                padding: 0 20px;
            }

            .YF28-header[data-v-d0b9b47a] {
                margin-top: 20px;
                padding: 30px;
                margin-bottom: 20px;
                border-radius: 20px;
            }

            .YF28-header>.YF28-header-main>.text>p[data-v-d0b9b47a]:first-child {
                font-size: 28px;
                margin: 10px 0;
            }

            .YF28-header>.YF28-header-main>.text>p[data-v-d0b9b47a]:nth-child(2) {
                font-size: 16px;
            }

            .td-1[data-v-d0b9b47a] {
                width: 80px;
                left: 50px;
                top: 20px;
            }

            .td-2[data-v-d0b9b47a] {
                width: 30px;
            }

            .YF28-nav[data-v-d0b9b47a] {
                position: absolute;
                top: 20px;
                right: 20px;
            }

            .YF28-nav a[data-v-d0b9b47a] {
                background: rgba(255,255,255,0.2);
                padding: 8px 16px;
                border-radius: 20px;
                color: white;
                text-decoration: none;
                font-size: 14px;
                transition: all 0.3s;
            }

            .YF28-nav a[data-v-d0b9b47a]:hover {
                background: rgba(255,255,255,0.3);
            }

            .warp[data-v-7cd14dad] {
                height: auto;
                overflow: visible;
                margin-top: 20px;
            }

            .qr-container {
                display: flex;
                justify-content: center;
                gap: 40px;
                padding: 40px;
                border-radius: 15px;
                background: rgba(255,255,255,0.8);
                backdrop-filter: blur(10px);
                margin: 20px 0;
            }

            .qr-input-section {
                flex: 1;
                max-width: 500px;
            }

            .qr-output-section {
                flex: 1;
                max-width: 500px;
                text-align: center;
            }

            .footer-sj[data-v-7cd14dad] {
                margin-top: 40px;
                font-size: 14px;
                padding-top: 20px;
            }
        }

        /* 移动端样式 */
        @media (max-width: 767px) {
            .qr-container {
                display: block;
                padding: 20px;
                border-radius: 15px;
                background: rgba(255,255,255,0.8);
                backdrop-filter: blur(10px);
                margin: 20px 10px;
            }

            .qr-input-section {
                margin-bottom: 30px;
            }

            .qr-output-section {
                text-align: center;
            }
        }

        /* 二维码生成器专用样式 */
        .qr-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
            text-align: center;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }

        .form-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
            box-sizing: border-box;
        }

        .form-input:focus {
            outline: none;
            border-color: #007bff;
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            background: white;
            cursor: pointer;
        }

        .btn-generate {
            width: 100%;
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn-generate:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-generate:active {
            transform: translateY(0);
        }

        .qr-result {
            margin-top: 20px;
        }

        .qr-code-display {
            display: inline-block;
            padding: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .btn-download {
            padding: 10px 20px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin: 0 5px;
            transition: background 0.3s;
        }

        .btn-download:hover {
            background: #218838;
        }

        .error-message {
            color: #dc3545;
            font-size: 14px;
            margin-top: 5px;
        }

        .success-message {
            color: #28a745;
            font-size: 14px;
            margin-top: 5px;
        }
    </style>
</head>

<body inmaintabuse="1">
    <div id="app" data-v-app="">
        <div data-v-7cd14dad="" class="YF28">
            <div data-v-d0b9b47a="" data-v-7cd14dad="" class="YF28-header">
                <div data-v-d0b9b47a="" class="YF28-header-main">
                    <img data-v-d0b9b47a=""
                        src="data:image/png;base64,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"
                        alt="" class="td-1">
                    <img data-v-d0b9b47a=""
                        src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADEAAAAoCAYAAABXRRJPAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAZdEVYdFNvZnR3YXJlAEFkb2JlIEltYWdlUmVhZHlxyWU8AAADYUlEQVRYR9WZaU8UQRCGuRU0igkigigQRUICwagREI3igSZEEIwcghwCP1N/Gj5vUoY5ao6ePVjf5Pmy01X1znZPbfdsW606Pz/vg/swB4vwHj4aH+AVPIUJ6Id2C708yQQMgwzvwFkgP2EZdOOWtUmiYA/MwjZ45qpwAA+hsbNDgU6QeX2DnpF6cAJ3rWR9ReJB+G6FmoGWZ6+Vr00kEs/gFLxijWYBqi8xgrtAncVL3kx2oc9slRdBWv/rlqQVOIQhs1csBqt1rlpwK3EMw2YzXwx8EwlsNTQj/WbVFwPUhbzgEPZhBfRQ6td7Bp6DvpwNqLVJbEGPWY6LC1pG6tNeYBH6sZqHAUuXKcZcgUn4Al6uMry1dHFxQSa8gDx+gfZD3ZYmSMRp2/IVvNxFjFmaC/Ghvp29yKAiNiF/fZYQObQCtOxCl5lab6eluRAfdsMLKFpWn6DLwuoi8mlW9OB69bKYtfC0uHgD9HB6gZ+hw4bWVeRVY1Er9ep6aB+Xno2oGHAPonum7M4Q0fbvP+3QD2PGKFyzy7ki/3ikXhkmLTRbDOoAtUl1oEH72JWMwgLsw5nDJkxB7kxSZwk8wx7rFlYsBuc+A2buGDzzSXQzmU2BWno2Q7b84XurpDA0FzFYlkO4bSlSwpjOLp5hj2kLqyaMaM17JsugZeeeGzCm2TiKGM3jnYWFCwNdsGeGqrJs6VKSuYTZLHYtJFwYmE4Yqsp1SxkT5h4lzOZRadegm1hNmKnKE0sZE8ZuJozmccvCwkTxst2oiBVLGRPG1N7LbkdGLay8KCw8Q1XYsrQpYU6/T57pJOkNYZEo3J0wUgvfLG1KmNNGzzOdZNxCwkTxWjvTP9YsZUqYK7uXCp8JieIrCTNVWbKUMWFMByjPsEf5FwlRUXwiYaYqI5YyJoxpe+4Z9qh2tqF4B+xEzFRhAyxjXBjTizvPcBKdf6ofDzAwAqdmKJQTyNwZY6zsq9PMxlBaGKm6f5qyFClhbChhNA/3mQoWhq5C2aV1BBMW6gpjIS/wHlhYfYS5ITPpmT+Al1C4/8fYKJRZTmrBDTku62Z0PJ2EGXgMdyComMyBzhV5LxAWbXhrC6O98DpiPEq13etlCcMDsBa5gXm79H8J40L/7/2AxjwLzVL6Btra/gIPMo2eXmEMCwAAAABJRU5ErkJggg=="
                        alt="" class="td-2">
                    <div data-v-d0b9b47a="" class="text">
                        <p data-v-d0b9b47a="">二维码生成器</p>
                        <p data-v-d0b9b47a="">快速生成各种类型的二维码</p>
                    </div>
                </div>
                <div data-v-d0b9b47a="" class="YF28-nav">
                    <a data-v-d0b9b47a="" href="../../Application/Index/View/Utils/index.html">返回工具库</a>
                </div>
            </div>

            <div data-v-7cd14dad="" class="warp">
                <div class="qr-container">
                    <div class="qr-input-section">
                        <h2 class="qr-title">输入内容</h2>

                        <div class="form-group">
                            <label class="form-label">内容类型</label>
                            <select id="contentType" class="form-select">
                                <option value="text">文本</option>
                                <option value="url">网址</option>
                                <option value="email">邮箱</option>
                                <option value="phone">电话</option>
                                <option value="sms">短信</option>
                                <option value="wifi">WiFi</option>
                            </select>
                        </div>

                        <div class="form-group" id="textGroup">
                            <label class="form-label">文本内容</label>
                            <textarea id="textContent" class="form-input form-textarea" placeholder="请输入要生成二维码的文本内容..."></textarea>
                        </div>

                        <div class="form-group" id="urlGroup" style="display: none;">
                            <label class="form-label">网址</label>
                            <input type="url" id="urlContent" class="form-input" placeholder="https://example.com">
                        </div>

                        <div class="form-group" id="emailGroup" style="display: none;">
                            <label class="form-label">邮箱地址</label>
                            <input type="email" id="emailContent" class="form-input" placeholder="<EMAIL>">
                            <label class="form-label" style="margin-top: 10px;">邮件主题（可选）</label>
                            <input type="text" id="emailSubject" class="form-input" placeholder="邮件主题">
                            <label class="form-label" style="margin-top: 10px;">邮件内容（可选）</label>
                            <textarea id="emailBody" class="form-input" placeholder="邮件内容"></textarea>
                        </div>

                        <div class="form-group" id="phoneGroup" style="display: none;">
                            <label class="form-label">电话号码</label>
                            <input type="tel" id="phoneContent" class="form-input" placeholder="+86 138 0000 0000">
                        </div>

                        <div class="form-group" id="smsGroup" style="display: none;">
                            <label class="form-label">手机号码</label>
                            <input type="tel" id="smsPhone" class="form-input" placeholder="+86 138 0000 0000">
                            <label class="form-label" style="margin-top: 10px;">短信内容</label>
                            <textarea id="smsContent" class="form-input" placeholder="短信内容"></textarea>
                        </div>

                        <div class="form-group" id="wifiGroup" style="display: none;">
                            <label class="form-label">WiFi名称</label>
                            <input type="text" id="wifiSSID" class="form-input" placeholder="WiFi网络名称">
                            <label class="form-label" style="margin-top: 10px;">WiFi密码</label>
                            <input type="password" id="wifiPassword" class="form-input" placeholder="WiFi密码">
                            <label class="form-label" style="margin-top: 10px;">加密类型</label>
                            <select id="wifiSecurity" class="form-select">
                                <option value="WPA">WPA/WPA2</option>
                                <option value="WEP">WEP</option>
                                <option value="nopass">无密码</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">二维码大小</label>
                            <select id="qrSize" class="form-select">
                                <option value="200">小 (200x200)</option>
                                <option value="300" selected>中 (300x300)</option>
                                <option value="400">大 (400x400)</option>
                                <option value="500">超大 (500x500)</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">容错级别</label>
                            <select id="errorLevel" class="form-select">
                                <option value="L">低 (7%)</option>
                                <option value="M" selected>中 (15%)</option>
                                <option value="Q">较高 (25%)</option>
                                <option value="H">高 (30%)</option>
                            </select>
                        </div>

                        <button id="generateBtn" class="btn-generate">生成二维码</button>
                        <div id="errorMsg" class="error-message" style="display: none;"></div>
                    </div>

                    <div class="qr-output-section">
                        <h2 class="qr-title">二维码预览</h2>
                        <div id="qrResult" class="qr-result" style="display: none;">
                            <div id="qrDisplay" class="qr-code-display"></div>
                            <div>
                                <button id="downloadPNG" class="btn-download">下载PNG</button>
                                <button id="downloadSVG" class="btn-download">下载SVG</button>
                            </div>
                            <div id="successMsg" class="success-message" style="display: none;"></div>
                        </div>
                        <div id="qrPlaceholder" style="color: #999; padding: 50px; text-align: center;">
                            请在左侧输入内容并点击生成按钮
                        </div>
                    </div>
                </div>
            </div>

            <div data-v-4ad322e2="" data-v-7cd14dad="" class="footer-sj" style="margin-top: 20vw;">
                <span data-v-4ad322e2="">二维码生成器<sup data-v-4ad322e2="">®</sup></span>
            </div>
        </div>
    </div>

    <script>
        // 二维码生成器类
        class QRCodeGenerator {
            constructor() {
                this.initializeElements();
                this.bindEvents();
            }

            initializeElements() {
                this.contentType = document.getElementById('contentType');
                this.textContent = document.getElementById('textContent');
                this.urlContent = document.getElementById('urlContent');
                this.emailContent = document.getElementById('emailContent');
                this.emailSubject = document.getElementById('emailSubject');
                this.emailBody = document.getElementById('emailBody');
                this.phoneContent = document.getElementById('phoneContent');
                this.smsPhone = document.getElementById('smsPhone');
                this.smsContent = document.getElementById('smsContent');
                this.wifiSSID = document.getElementById('wifiSSID');
                this.wifiPassword = document.getElementById('wifiPassword');
                this.wifiSecurity = document.getElementById('wifiSecurity');
                this.qrSize = document.getElementById('qrSize');
                this.errorLevel = document.getElementById('errorLevel');
                this.generateBtn = document.getElementById('generateBtn');
                this.qrResult = document.getElementById('qrResult');
                this.qrDisplay = document.getElementById('qrDisplay');
                this.qrPlaceholder = document.getElementById('qrPlaceholder');
                this.errorMsg = document.getElementById('errorMsg');
                this.successMsg = document.getElementById('successMsg');
                this.downloadPNG = document.getElementById('downloadPNG');
                this.downloadSVG = document.getElementById('downloadSVG');

                // 内容组
                this.contentGroups = {
                    text: document.getElementById('textGroup'),
                    url: document.getElementById('urlGroup'),
                    email: document.getElementById('emailGroup'),
                    phone: document.getElementById('phoneGroup'),
                    sms: document.getElementById('smsGroup'),
                    wifi: document.getElementById('wifiGroup')
                };
            }

            bindEvents() {
                this.contentType.addEventListener('change', () => this.switchContentType());
                this.generateBtn.addEventListener('click', () => this.generateQRCode());
                this.downloadPNG.addEventListener('click', () => this.downloadQRCode('png'));
                this.downloadSVG.addEventListener('click', () => this.downloadQRCode('svg'));
            }

            switchContentType() {
                const selectedType = this.contentType.value;

                // 隐藏所有内容组
                Object.values(this.contentGroups).forEach(group => {
                    group.style.display = 'none';
                });

                // 显示选中的内容组
                if (this.contentGroups[selectedType]) {
                    this.contentGroups[selectedType].style.display = 'block';
                }
            }

            getContentString() {
                const type = this.contentType.value;
                let content = '';

                switch (type) {
                    case 'text':
                        content = this.textContent.value.trim();
                        break;
                    case 'url':
                        content = this.urlContent.value.trim();
                        if (content && !content.startsWith('http://') && !content.startsWith('https://')) {
                            content = 'https://' + content;
                        }
                        break;
                    case 'email':
                        const email = this.emailContent.value.trim();
                        const subject = this.emailSubject.value.trim();
                        const body = this.emailBody.value.trim();
                        content = `mailto:${email}`;
                        if (subject || body) {
                            const params = [];
                            if (subject) params.push(`subject=${encodeURIComponent(subject)}`);
                            if (body) params.push(`body=${encodeURIComponent(body)}`);
                            content += '?' + params.join('&');
                        }
                        break;
                    case 'phone':
                        content = `tel:${this.phoneContent.value.trim()}`;
                        break;
                    case 'sms':
                        const phone = this.smsPhone.value.trim();
                        const smsText = this.smsContent.value.trim();
                        content = `sms:${phone}`;
                        if (smsText) {
                            content += `?body=${encodeURIComponent(smsText)}`;
                        }
                        break;
                    case 'wifi':
                        const ssid = this.wifiSSID.value.trim();
                        const password = this.wifiPassword.value.trim();
                        const security = this.wifiSecurity.value;
                        content = `WIFI:T:${security};S:${ssid};P:${password};;`;
                        break;
                }

                return content;
            }

            validateContent() {
                const type = this.contentType.value;
                const content = this.getContentString();

                if (!content) {
                    return '请输入内容';
                }

                switch (type) {
                    case 'url':
                        if (!this.isValidURL(content)) {
                            return '请输入有效的网址';
                        }
                        break;
                    case 'email':
                        if (!this.emailContent.value.trim() || !this.isValidEmail(this.emailContent.value.trim())) {
                            return '请输入有效的邮箱地址';
                        }
                        break;
                    case 'phone':
                    case 'sms':
                        const phoneValue = type === 'phone' ? this.phoneContent.value.trim() : this.smsPhone.value.trim();
                        if (!phoneValue) {
                            return '请输入电话号码';
                        }
                        break;
                    case 'wifi':
                        if (!this.wifiSSID.value.trim()) {
                            return '请输入WiFi名称';
                        }
                        break;
                }

                return null;
            }

            isValidURL(string) {
                try {
                    new URL(string);
                    return true;
                } catch (_) {
                    return false;
                }
            }

            isValidEmail(email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(email);
            }

            showError(message) {
                this.errorMsg.textContent = message;
                this.errorMsg.style.display = 'block';
                setTimeout(() => {
                    this.errorMsg.style.display = 'none';
                }, 5000);
            }

            showSuccess(message) {
                this.successMsg.textContent = message;
                this.successMsg.style.display = 'block';
                setTimeout(() => {
                    this.successMsg.style.display = 'none';
                }, 3000);
            }

            generateQRCode() {
                // 验证输入
                const validationError = this.validateContent();
                if (validationError) {
                    this.showError(validationError);
                    return;
                }

                const content = this.getContentString();
                const size = parseInt(this.qrSize.value);
                const errorCorrectionLevel = this.errorLevel.value;

                try {
                    // 创建二维码
                    const qr = qrcode(0, errorCorrectionLevel);
                    qr.addData(content);
                    qr.make();

                    // 清空显示区域
                    this.qrDisplay.innerHTML = '';

                    // 创建二维码图像
                    const qrCanvas = this.createQRCanvas(qr, size);
                    this.qrDisplay.appendChild(qrCanvas);

                    // 显示结果区域
                    this.qrPlaceholder.style.display = 'none';
                    this.qrResult.style.display = 'block';

                    // 存储当前二维码数据用于下载
                    this.currentQRData = {
                        qr: qr,
                        content: content,
                        size: size
                    };

                    this.showSuccess('二维码生成成功！');

                } catch (error) {
                    console.error('生成二维码时出错:', error);
                    this.showError('生成二维码失败，请检查输入内容');
                }
            }

            createQRCanvas(qr, size) {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const modules = qr.getModuleCount();
                const cellSize = Math.floor(size / modules);
                const actualSize = cellSize * modules;

                canvas.width = actualSize;
                canvas.height = actualSize;

                // 设置背景为白色
                ctx.fillStyle = '#FFFFFF';
                ctx.fillRect(0, 0, actualSize, actualSize);

                // 绘制二维码
                ctx.fillStyle = '#000000';
                for (let row = 0; row < modules; row++) {
                    for (let col = 0; col < modules; col++) {
                        if (qr.isDark(row, col)) {
                            ctx.fillRect(col * cellSize, row * cellSize, cellSize, cellSize);
                        }
                    }
                }

                return canvas;
            }

            downloadQRCode(format) {
                if (!this.currentQRData) {
                    this.showError('请先生成二维码');
                    return;
                }

                const canvas = this.qrDisplay.querySelector('canvas');
                if (!canvas) {
                    this.showError('二维码数据不存在');
                    return;
                }

                try {
                    if (format === 'png') {
                        // 下载PNG格式
                        const link = document.createElement('a');
                        link.download = `qrcode_${Date.now()}.png`;
                        link.href = canvas.toDataURL('image/png');
                        link.click();
                        this.showSuccess('PNG文件下载成功！');
                    } else if (format === 'svg') {
                        // 生成SVG格式
                        const svgData = this.generateSVG(this.currentQRData.qr, this.currentQRData.size);
                        const blob = new Blob([svgData], { type: 'image/svg+xml' });
                        const url = URL.createObjectURL(blob);
                        const link = document.createElement('a');
                        link.download = `qrcode_${Date.now()}.svg`;
                        link.href = url;
                        link.click();
                        URL.revokeObjectURL(url);
                        this.showSuccess('SVG文件下载成功！');
                    }
                } catch (error) {
                    console.error('下载失败:', error);
                    this.showError('下载失败，请重试');
                }
            }

            generateSVG(qr, size) {
                const modules = qr.getModuleCount();
                const cellSize = size / modules;

                let svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${size}" height="${size}" viewBox="0 0 ${size} ${size}">`;
                svg += `<rect width="${size}" height="${size}" fill="#FFFFFF"/>`;

                for (let row = 0; row < modules; row++) {
                    for (let col = 0; col < modules; col++) {
                        if (qr.isDark(row, col)) {
                            const x = col * cellSize;
                            const y = row * cellSize;
                            svg += `<rect x="${x}" y="${y}" width="${cellSize}" height="${cellSize}" fill="#000000"/>`;
                        }
                    }
                }

                svg += '</svg>';
                return svg;
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            new QRCodeGenerator();
        });
    </script>
</body>

</html>
